// Global Online Status Management
// This script runs on every page to maintain user online status

(function() {
  'use strict';

  try {
    // Only run if user is logged in and not on login page
    if (!window.ENV || !window.ENV.current_user_id || window.location.pathname.includes('/login')) {
      console.log('🌐 Global Online Status: Skipping - not logged in or on login page');
      return;
    }
  } catch (error) {
    console.error('🌐 Global Online Status: Error during initialization check:', error);
    return;
  }

  console.log('🌐 Global Online Status: Initializing for user', window.ENV.current_user_id);

  try {
    let statusUpdateInterval;
  let isOnline = true;
  let lastActivityTime = Date.now();

  // Configuration
  const CONFIG = {
    UPDATE_INTERVAL: 30000, // Update status every 30 seconds
    ACTIVITY_TIMEOUT: 5 * 60 * 1000, // 5 minutes of inactivity before considering offline
    HEARTBEAT_ENDPOINT: '/api/v1/chat/update_status'
  };

  // Initialize online status
  function initializeOnlineStatus() {
    console.log('🟢 Global Online Status: Marking user online');
    
    // Mark user as online immediately
    updateOnlineStatus(true);
    
    // Set up periodic status updates
    setupPeriodicUpdates();
    
    // Set up activity tracking
    setupActivityTracking();
    
    // Set up page visibility handling
    setupVisibilityHandling();
    
    // Set up beforeunload handling
    setupBeforeUnloadHandling();
  }

  // Update online status on server
  function updateOnlineStatus(online, callback) {
    // Check if we have the required elements
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (!csrfToken) {
      console.warn('⚠️ Global Online Status: No CSRF token found, skipping update');
      if (callback) callback(false);
      return;
    }

    const data = JSON.stringify({ online: online });

    fetch(CONFIG.HEARTBEAT_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken
      },
      credentials: 'same-origin',
      body: data
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response.json();
    })
    .then(data => {
      if (data.success) {
        console.log(`🔄 Global Online Status: Updated to ${online ? 'online' : 'offline'}`);
      } else {
        console.warn('⚠️ Global Online Status: Update failed', data.error || 'Unknown error');
      }
      if (callback) callback(data.success);
    })
    .catch(error => {
      console.error('❌ Global Online Status: Update error', error);
      if (callback) callback(false);
    });
  }

  // Set up periodic status updates
  function setupPeriodicUpdates() {
    statusUpdateInterval = setInterval(() => {
      const timeSinceActivity = Date.now() - lastActivityTime;
      const shouldBeOnline = timeSinceActivity < CONFIG.ACTIVITY_TIMEOUT;
      
      if (shouldBeOnline !== isOnline) {
        isOnline = shouldBeOnline;
        console.log(`🔄 Global Online Status: Activity-based status change to ${isOnline ? 'online' : 'offline'}`);
        updateOnlineStatus(isOnline);
      } else if (isOnline) {
        // Send heartbeat to maintain online status
        updateOnlineStatus(true);
      }
    }, CONFIG.UPDATE_INTERVAL);
  }

  // Track user activity
  function setupActivityTracking() {
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    function updateActivity() {
      lastActivityTime = Date.now();
      
      // If user was offline due to inactivity, mark them online again
      if (!isOnline) {
        isOnline = true;
        console.log('🟢 Global Online Status: User active again, marking online');
        updateOnlineStatus(true);
      }
    }

    // Throttle activity updates to avoid excessive calls
    let activityTimeout;
    function throttledUpdateActivity() {
      if (activityTimeout) return;
      
      activityTimeout = setTimeout(() => {
        updateActivity();
        activityTimeout = null;
      }, 1000); // Throttle to once per second
    }

    activityEvents.forEach(event => {
      document.addEventListener(event, throttledUpdateActivity, true);
    });
  }

  // Handle page visibility changes
  function setupVisibilityHandling() {
    document.addEventListener('visibilitychange', function() {
      if (document.visibilityState === 'visible') {
        console.log('👁️ Global Online Status: Page visible, updating activity');
        lastActivityTime = Date.now();
        if (!isOnline) {
          isOnline = true;
          updateOnlineStatus(true);
        }
      } else {
        console.log('🙈 Global Online Status: Page hidden');
        // Don't immediately mark offline, let the activity timeout handle it
      }
    });
  }

  // Handle page unload
  function setupBeforeUnloadHandling() {
    window.addEventListener('beforeunload', function() {
      // Only mark offline if we're actually leaving the site (not just refreshing)
      // Use sendBeacon for reliability during page unload
      console.log('🚪 Global Online Status: Page unloading');
      
      const data = JSON.stringify({ online: false });
      const blob = new Blob([data], { type: 'application/json' });
      
      // Try sendBeacon first, fallback to synchronous request
      if (navigator.sendBeacon) {
        navigator.sendBeacon(CONFIG.HEARTBEAT_ENDPOINT, blob);
      } else {
        // Synchronous fallback (not recommended but necessary for older browsers)
        const xhr = new XMLHttpRequest();
        xhr.open('POST', CONFIG.HEARTBEAT_ENDPOINT, false);
        xhr.setRequestHeader('Content-Type', 'application/json');
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (csrfToken) {
          xhr.setRequestHeader('X-CSRF-Token', csrfToken);
        }
        try {
          xhr.send(data);
        } catch (e) {
          console.warn('⚠️ Global Online Status: Synchronous offline update failed', e);
        }
      }
    });
  }

  // Cleanup function
  function cleanup() {
    if (statusUpdateInterval) {
      clearInterval(statusUpdateInterval);
    }
  }

  // Handle auto-logout integration
  function integrateWithAutoLogout() {
    // Listen for the auto-logout warning and actual logout
    const originalAlert = window.alert;
    window.alert = function(message) {
      if (message && message.includes('logged out due to inactivity')) {
        console.log('⏰ Global Online Status: Auto-logout warning detected');
        // User is about to be logged out, mark as offline
        updateOnlineStatus(false);
      }
      return originalAlert.apply(this, arguments);
    };

    // Monitor for redirects to login page (auto-logout)
    // Safely check if we can override location methods
    try {
      const originalAssign = window.location.assign;
      const originalReplace = window.location.replace;

      function handleLogoutRedirect(url) {
        if (url && url.includes('/login')) {
          console.log('🚪 Global Online Status: Logout redirect detected');
          updateOnlineStatus(false);
        }
      }

      // Only override if the properties are writable
      if (originalAssign && typeof originalAssign === 'function') {
        try {
          window.location.assign = function(url) {
            handleLogoutRedirect(url);
            return originalAssign.call(this, url);
          };
        } catch (e) {
          console.warn('⚠️ Global Online Status: Cannot override location.assign:', e.message);
        }
      }

      if (originalReplace && typeof originalReplace === 'function') {
        try {
          window.location.replace = function(url) {
            handleLogoutRedirect(url);
            return originalReplace.call(this, url);
          };
        } catch (e) {
          console.warn('⚠️ Global Online Status: Cannot override location.replace:', e.message);
        }
      }
    } catch (error) {
      console.warn('⚠️ Global Online Status: Error setting up location monitoring:', error.message);
    }
  }

  // Initialize when DOM is ready
  try {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', function() {
        try {
          initializeOnlineStatus();
        } catch (error) {
          console.error('🌐 Global Online Status: Error during DOM ready initialization:', error);
        }
      });
    } else {
      initializeOnlineStatus();
    }
  } catch (error) {
    console.error('🌐 Global Online Status: Error during setup:', error);
  }

  // Integrate with auto-logout system
  integrateWithAutoLogout();

  // Cleanup on page unload
  window.addEventListener('unload', cleanup);

  // Expose for debugging
  window.GlobalOnlineStatus = {
    updateStatus: updateOnlineStatus,
    isOnline: () => isOnline,
    lastActivity: () => new Date(lastActivityTime),
    config: CONFIG
  };

    console.log('✅ Global Online Status: Initialized successfully');

  } catch (globalError) {
    console.error('🌐 Global Online Status: Critical error in global scope:', globalError);
  }
})();
